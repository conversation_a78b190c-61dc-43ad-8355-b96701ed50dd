import React, { useState, useEffect, useRef } from 'react';

const sections = [
  { id: 'build-concept', title: '1. Build Concept' },
  { id: 'core-setup', title: '2. Core Setup' },
  { id: 'leveling-walkthrough', title: '3. Leveling Walkthrough' },
  { id: 'early-game-gear', title: '4. Early Game Gear' },
  { id: 'transition-reap', title: '5. Transition to Reap' },
  { id: 'mid-end-game-gear', title: '6. Mid/End-Game Gear' },
  { id: 'atlas-strategy', title: '7. Atlas Strategy' },
  { id: 'defences-flasks', title: '8. Defences & Flasks' },
  { id: 'final-gem-links', title: '9. Final Gem Links' },
  { id: 'checklist-overview', title: '10. Checklist Overview' },
];

function Sidebar() {
  const [activeSection, setActiveSection] = useState('');
  const observer = useRef(null);

  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        const firstVisible = entries.find((entry) => entry.isIntersecting);
        if (firstVisible) {
          setActiveSection(firstVisible.target.id);
        }
      },
      { rootMargin: '-20% 0px -80% 0px', threshold: 0 }
    );

    const elements = document.querySelectorAll('section[id]');
    elements.forEach((el) => observer.current.observe(el));

    return () => {
      elements.forEach((el) => observer.current.unobserve(el));
    };
  }, []);

  return (
    <aside className="w-72 bg-slate-800/60 backdrop-blur-md p-6 overflow-y-auto border-r border-white/10 shadow-xl">
      <div className="mb-8">
        <h2 className="text-xl font-bold bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent mb-2">
          Guide Sections
        </h2>
        <div className="w-12 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
      </div>
      <nav>
        <ul className="space-y-2">
          {sections.map((section, index) => (
            <li key={section.id} className="group">
              <a
                href={`#${section.id}`}
                className={`nav-item ${
                  activeSection === section.id ? 'active' : ''
                }`}
              >
                <div className="flex items-center space-x-3 relative z-10">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-xs font-bold transition-all duration-300 ${
                    activeSection === section.id
                      ? 'bg-white/20 text-white'
                      : 'bg-indigo-500/20 text-indigo-400 group-hover:bg-indigo-500/30'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="flex-1 truncate">
                    {section.title}
                  </span>
                  {activeSection === section.id && (
                    <div className="w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
                  )}
                </div>
              </a>
            </li>
          ))}
        </ul>
      </nav>

      {/* Progress Indicator */}
      <div className="mt-8 p-4 bg-slate-900/50 rounded-xl border border-white/10">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-slate-400">Progress</span>
          <span className="text-sm font-bold text-amber-400">
            {Math.round(((sections.findIndex(s => s.id === activeSection) + 1) / sections.length) * 100)}%
          </span>
        </div>
        <div className="w-full bg-slate-800 rounded-full h-2">
          <div
            className="bg-gradient-to-r from-indigo-500 to-blue-500 h-2 rounded-full transition-all duration-500 ease-out"
            style={{
              width: `${((sections.findIndex(s => s.id === activeSection) + 1) / sections.length) * 100}%`
            }}
          ></div>
        </div>
      </div>
    </aside>
  );
}

export default Sidebar;