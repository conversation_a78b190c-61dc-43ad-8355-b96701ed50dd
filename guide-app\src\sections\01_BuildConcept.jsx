import React from 'react';

const BuildConcept = () => {
  return (
    <section id="build-concept" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Build Concept: Physical Trapper/Miner to Cold Conversion Exsanguinate/Reap
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-6">
          <div className="bg-slate-900/30 p-6 rounded-xl border border-indigo-500/20">
            <p className="text-slate-300 leading-relaxed text-lg">
              This build starts by leveling with a generic physical skill, transitions into a powerful mid-game setup using Power Siphon Mines, and ultimately converts to a Cold-based Exsanguinate or Reap build for endgame content. The core idea is to leverage the high clear speed of mines and the immense single-target potential of physical spells converted to cold, which provides excellent crowd control through freeze.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {/* Pros Section */}
            <div className="bg-gradient-to-br from-green-900/20 to-green-800/10 p-6 rounded-xl border border-green-500/20">
              <h3 className="text-xl font-semibold text-green-400 mb-4 flex items-center">
                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Pros
              </h3>
              <ul className="space-y-3">
                {[
                  "S-tier mapping, exceptional clear speed.",
                  "Scales well as a Physical Skill, offering high damage potential.",
                  "Freeze is an incredibly strong defensive layer.",
                  "Very fast movement and clear.",
                  "Reap offers a higher damage upside for bossing if you're willing to gem swap.",
                  "Extremely high damage ceiling, capable of reaching 100-400 million DPS with investment.",
                  "If you prefer not to use Reap, you can skip it entirely and still handle bosses effectively with sufficient investment."
                ].map((pro, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300">{pro}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Cons Section */}
            <div className="bg-gradient-to-br from-red-900/20 to-red-800/10 p-6 rounded-xl border border-red-500/20">
              <h3 className="text-xl font-semibold text-red-400 mb-4 flex items-center">
                <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Cons
              </h3>
              <ul className="space-y-3">
                {[
                  "Gem swapping is often necessary for optimal bossing damage.",
                  "Reap requires extra sockets to generate Blood charges efficiently.",
                  "Using Reap adds an extra button to your rotation.",
                  "Reap has a significant life cost, which requires mitigation."
                ].map((con, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-slate-300">{con}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BuildConcept;