import React from 'react';

const FinalGemLinks = () => {
  const levelingPhases = [
    {
      title: "Early Leveling (Levels 1-12)",
      color: "emerald",
      icon: "M13 10V3L4 14h7v7l9-11h-7z",
      links: [
        {
          purpose: "Clear",
          gems: ["Rolling Magma", "Elemental Proliferation"],
          notes: "Start with this basic 2-link after Tidal Island"
        },
        {
          purpose: "Utility",
          gems: ["Flame Wall"],
          notes: "Cast magma through flame wall for extra fire damage"
        },
        {
          purpose: "Single Target",
          gems: ["Fire Trap"],
          notes: "Pick up at level 12 for single target damage"
        }
      ]
    },
    {
      title: "Mid Leveling (Levels 13-31)",
      color: "blue",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
      links: [
        {
          purpose: "Clear",
          gems: ["Rolling Magma", "Elemental Proliferation", "Combustion"],
          notes: "3-link setup for clearing packs"
        },
        {
          purpose: "Single Target",
          gems: ["Fire Trap", "Trap and Mine Damage", "Swift Assembly"],
          notes: "3-link setup for single target damage"
        },
        {
          purpose: "Support",
          gems: ["Added Cold Damage", "Added Lightning Damage"],
          notes: "Add damage supports as you find links"
        }
      ]
    },
    {
      title: "Power Siphon Phase (Levels 31-88)",
      color: "purple",
      icon: "M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4",
      links: [
        {
          purpose: "Main Skill",
          gems: ["Power Siphon", "Locus Mines", "Charged Mines"],
          notes: "Transition at level 31 after Library quest"
        },
        {
          purpose: "Aura",
          gems: ["Grace"],
          notes: "Essential defensive aura from Act 4"
        },
        {
          purpose: "Off-hand Leveling",
          gems: ["Exsanguinate (multiple copies)"],
          notes: "Level in every available socket for corruption"
        }
      ]
    }
  ];

  const endgameSetups = [
    {
      title: "Exsanguinate (Primary 6-Link)",
      color: "red",
      purpose: "Clear & Single Target",
      gems: ["Exsanguinate", "Unleash", "Cold Penetration", "Increased Critical Damage", "Increased Critical Strikes", "Empower"],
      notes: "Your main damage setup in chest piece",
      alternatives: "For clear: swap Increased Critical Damage → Awakened Chain"
    },
    {
      title: "Reap (Bossing Alternative)",
      color: "orange",
      purpose: "Maximum Single Target",
      gems: ["Reap", "Spell Cascade", "Cold Penetration", "Increased Critical Damage", "Concentrated Effect", "Empower"],
      notes: "Swap for difficult bosses",
      alternatives: "Requires gem swapping and blood charge generation"
    }
  ];

  const utilitySetups = [
    {
      category: "Auras",
      setups: [
        { gems: ["Grace"], priority: "Essential" },
        { gems: ["Hatred"], priority: "High" },
        { gems: ["Determination"], priority: "Optional (if mana allows)" },
        { gems: ["Purity of Elements"], priority: "Situational (ailment immunity)" }
      ]
    },
    {
      category: "Movement & Utility",
      setups: [
        { gems: ["Flame Dash", "Arcane Surge"], priority: "Movement" },
        { gems: ["Assassin's Mark", "Mark on Hit"], priority: "Curse" },
        { gems: ["Stone Golem"], priority: "Life Regen" },
        { gems: ["Ice Golem"], priority: "Crit Chance Alternative" },
        { gems: ["Molten Shell", "Cast when Damage Taken"], priority: "Defense" }
      ]
    }
  ];

  return (
    <section id="final-gem-links" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Complete Gem Links Guide
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Leveling Progression */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-indigo-500/20">
            <h3 className="text-xl font-semibold text-indigo-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              Leveling Gem Progression
            </h3>
            <div className="space-y-6">
              {levelingPhases.map((phase, index) => (
                <div key={index} className={`bg-slate-800/50 p-5 rounded-lg border border-${phase.color}-500/20`}>
                  <h4 className={`text-lg font-semibold text-${phase.color}-400 mb-4 flex items-center`}>
                    <div className={`w-8 h-8 bg-${phase.color}-500/20 text-${phase.color}-400 rounded-lg flex items-center justify-center mr-3`}>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={phase.icon} />
                      </svg>
                    </div>
                    {phase.title}
                  </h4>
                  <div className="grid gap-4">
                    {phase.links.map((link, linkIndex) => (
                      <div key={linkIndex} className="bg-slate-900/50 p-4 rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className={`text-${phase.color}-400 font-medium`}>{link.purpose}</span>
                          <span className="text-xs text-slate-400">{link.gems.length}-Link</span>
                        </div>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {link.gems.map((gem, gemIndex) => (
                            <span key={gemIndex} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                              {gem}
                            </span>
                          ))}
                        </div>
                        <p className="text-slate-300 text-sm">{link.notes}</p>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Endgame 6-Link Setups */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-red-500/20">
            <h3 className="text-xl font-semibold text-red-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
              Endgame 6-Link Setups
            </h3>
            <div className="space-y-6">
              {endgameSetups.map((setup, index) => (
                <div key={index} className={`bg-slate-800/50 p-5 rounded-lg border border-${setup.color}-500/20`}>
                  <div className="flex items-center justify-between mb-4">
                    <h4 className={`text-lg font-semibold text-${setup.color}-400`}>{setup.title}</h4>
                    <span className={`px-3 py-1 bg-${setup.color}-500/20 text-${setup.color}-400 text-sm rounded-full`}>
                      {setup.purpose}
                    </span>
                  </div>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {setup.gems.map((gem, gemIndex) => (
                      <span key={gemIndex} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                        {gem}
                      </span>
                    ))}
                  </div>
                  <p className="text-slate-300 text-sm mb-2">{setup.notes}</p>
                  {setup.alternatives && (
                    <div className="bg-blue-900/20 p-3 rounded-lg border border-blue-500/30">
                      <p className="text-blue-200 text-sm">
                        <span className="font-semibold">Alternative:</span> {setup.alternatives}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Utility Setups */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-green-500/20">
            <h3 className="text-xl font-semibold text-green-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              </svg>
              Utility & Support Setups
            </h3>
            <div className="grid gap-6">
              {utilitySetups.map((category, index) => (
                <div key={index} className="bg-slate-800/50 p-5 rounded-lg">
                  <h4 className="text-green-400 font-semibold mb-4">{category.category}</h4>
                  <div className="space-y-3">
                    {category.setups.map((setup, setupIndex) => (
                      <div key={setupIndex} className="flex items-center justify-between p-3 bg-slate-900/50 rounded-lg">
                        <div className="flex flex-wrap gap-2">
                          {setup.gems.map((gem, gemIndex) => (
                            <span key={gemIndex} className="px-3 py-1 bg-amber-500/20 text-amber-300 text-sm rounded-full font-medium">
                              {gem}
                            </span>
                          ))}
                        </div>
                        <span className="text-slate-400 text-sm ml-4">{setup.priority}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Important Notes */}
          <div className="bg-gradient-to-r from-amber-900/20 to-orange-900/20 p-6 rounded-xl border border-amber-500/30">
            <div className="flex items-start space-x-3">
              <svg className="w-6 h-6 text-amber-400 flex-shrink-0 mt-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="text-amber-400 font-semibold mb-3">Important Gem Link Notes</h4>
                <ul className="space-y-2 text-amber-200 text-sm">
                  <li>• <span className="font-semibold">Exsanguinate Quality:</span> Not important since we scale the hit, not DoT</li>
                  <li>• <span className="font-semibold">Reap Quality:</span> Highly desirable for damage per Blood Charge</li>
                  <li>• <span className="font-semibold">Level 21 Gems:</span> Massive damage increase - corrupt multiple copies</li>
                  <li>• <span className="font-semibold">Empower Level 4:</span> Huge damage boost but expensive</li>
                  <li>• <span className="font-semibold">Support Gem Priority:</span> Cold Penetration &gt; Crit Multi &gt; Crit Chance</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FinalGemLinks;