import React from 'react';

const CoreSetup = () => {
  return (
    <section id="core-setup" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Core Skill Setup & Off-hand Leveling
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Primary Leveling Skills */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-blue-500/20">
            <h3 className="text-xl font-semibold text-blue-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Primary Leveling Skills (Acts 1-3)
            </h3>
            <p className="text-slate-300 leading-relaxed">
              From level 1 to 31, we will primarily use <span className="text-amber-400 font-semibold">Rolling Magma</span> for clearing packs and <span className="text-amber-400 font-semibold">Fire Trap</span> for additional single-target damage. This setup provides a smooth experience until we transition to our mid-game setup.
            </p>
          </div>

          {/* Off-hand Gem Leveling */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-indigo-500/20">
            <h3 className="text-xl font-semibold text-indigo-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
              </svg>
              Off-hand Gem Leveling Strategy
            </h3>
            <div className="space-y-4">
              <p className="text-slate-300 leading-relaxed">
                Level <span className="text-amber-400 font-semibold">Exsanguinate</span> in every available off-hand socket. Your goal is to corrupt them for a level 21 gem as soon as possible, as each gem level provides a significant power increase. Quality is not important for Exsanguinate since we scale the hit, not the damage over time.
              </p>
              <div className="bg-amber-500/10 p-4 rounded-lg border border-amber-500/30">
                <p className="text-slate-300">
                  For <span className="text-amber-400 font-semibold">Reap</span>, however, quality is highly desirable as it provides a substantial damage boost per Blood Charge.
                </p>
              </div>
            </div>
          </div>

          {/* Ascendancy Order */}
          <div className="bg-gaming-bg/30 p-6 rounded-xl border border-gaming-cyan/20">
            <h3 className="text-xl font-semibold text-gaming-cyan mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
              </svg>
              Ascendancy Order
            </h3>
            <div className="space-y-4">
              {[
                { name: "One Step Ahead", desc: "Incredible for early mapping and safety.", priority: 1 },
                { name: "Swift Killer", desc: "Boosts charge generation and damage.", priority: 2 },
                { name: "Escape Artist", desc: "A massive defensive boost from your chest piece.", priority: 3 },
                { name: "Polymath or Spellbreaker", desc: "Choose Spellbreaker if you are not spell suppression capped. It's a superior defensive layer for Uber bossing. You can respec later if desired.", priority: 4 }
              ].map((ascendancy, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-gaming-bg/50 rounded-lg">
                  <div className="w-8 h-8 bg-gaming-cyan/20 text-gaming-cyan rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    {ascendancy.priority}
                  </div>
                  <div>
                    <h4 className="font-semibold text-gaming-gold mb-1">{ascendancy.name}</h4>
                    <p className="text-gaming-text-muted text-sm">{ascendancy.desc}</p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-6 bg-amber-900/20 p-4 rounded-lg border border-amber-500/30">
              <p className="text-amber-200 text-sm">
                <span className="font-semibold">Important Note:</span> For endgame bossing, it's highly recommended to respec <em>One Step Ahead</em> into whichever notable you didn't take fourth. The 10% spell suppression from Spellbreaker is often more valuable in Uber fights than the action speed immunity.
              </p>
            </div>
          </div>

          {/* Bandit Choice */}
          <div className="bg-gaming-bg/30 p-6 rounded-xl border border-gaming-red/20">
            <h3 className="text-xl font-semibold text-gaming-red mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Bandit Choice
            </h3>
            <p className="text-gaming-text-muted mb-4">There is no wrong choice, but here are the recommendations:</p>
            <div className="grid gap-4">
              {[
                { name: "Kraityn", benefit: "8% movement speed", desc: "Excellent for mapping", recommended: true },
                { name: "Oak", benefit: "+40 life", desc: "Solid defensive choice, especially for new players", recommended: false },
                { name: "Alira", benefit: "Resistances & mana regen", desc: "Helps with resistance gearing if you find it difficult", recommended: false }
              ].map((bandit, index) => (
                <div key={index} className={`p-4 rounded-lg border ${bandit.recommended ? 'bg-gaming-gold/10 border-gaming-gold/30' : 'bg-gaming-bg/50 border-white/10'}`}>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-semibold ${bandit.recommended ? 'text-gaming-gold' : 'text-gaming-text'}`}>
                      {bandit.name} {bandit.recommended && '(Recommended)'}
                    </h4>
                    <span className="text-sm text-gaming-purple font-medium">{bandit.benefit}</span>
                  </div>
                  <p className="text-gaming-text-muted text-sm">{bandit.desc}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CoreSetup;