import React from 'react';

const LevelingWalkthrough = () => {
  return (
    <section id="leveling-walkthrough" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Leveling Walkthrough: Acts 1-4
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Vendor Search Strings */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-purple-500/20">
            <h3 className="text-xl font-semibold text-purple-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Vendor Search Strings
            </h3>
            <p className="text-slate-300 leading-relaxed mb-4">Use this regex in the vendor window to quickly find useful items:</p>
            <div className="bg-slate-800 p-4 rounded-lg border border-slate-600">
              <code className="text-green-400 font-mono text-sm">b-b-b|g-g-b|g-[gb]-g|b-g-g|nne|rint|ll g|Earn</code>
            </div>
            <p className="text-slate-400 text-sm mt-3">This highlights linked sockets, movement speed boots, and +1 gem level wands.</p>
          </div>

          {/* Pre-Mine Gem Progression */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-blue-500/20">
            <h3 className="text-xl font-semibold text-blue-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
              Pre-Mine Gem Progression (Levels 1-30)
            </h3>
            <div className="space-y-4">
              {[
                { level: "Level 4 (Tidal Island)", content: "Buy Rolling Magma + Elemental Proliferation. Also, buy Flame Wall and cast your magma through it for extra fire damage." },
                { level: "Level 8 (Brutus)", content: "Add a damage support like Added Cold Damage or Added Lightning Damage. After killing Brutus, get Combustion from the vendor." },
                { level: "Level 12 (The Cavern of Wrath)", content: "Pick up Fire Trap for single target. Link it with Trap and Mine Damage in Act 2." }
              ].map((milestone, index) => (
                <div key={index} className="flex items-start space-x-4 p-4 bg-slate-800/50 rounded-lg">
                  <div className="w-8 h-8 bg-blue-500/20 text-blue-400 rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-semibold text-amber-400 mb-1">{milestone.level}</h4>
                    <p className="text-slate-300 text-sm leading-relaxed">
                      {milestone.content.split(/(\b(?:Rolling Magma|Elemental Proliferation|Flame Wall|Added Cold Damage|Added Lightning Damage|Combustion|Fire Trap|Trap and Mine Damage)\b)/g).map((part, i) =>
                        ['Rolling Magma', 'Elemental Proliferation', 'Flame Wall', 'Added Cold Damage', 'Added Lightning Damage', 'Combustion', 'Fire Trap', 'Trap and Mine Damage'].includes(part) ?
                        <span key={i} className="text-amber-400 font-semibold">{part}</span> : part
                      )}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Act 2 Setup */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-green-500/20">
            <h3 className="text-xl font-semibold text-green-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Act 2 Setup
            </h3>
            <p className="text-slate-300 mb-4">Your links should be:</p>
            <div className="grid gap-4">
              <div className="p-4 bg-green-900/20 rounded-lg border border-green-500/30">
                <h4 className="font-semibold text-green-400 mb-2">Clear</h4>
                <p className="text-slate-300 text-sm">
                  <span className="text-amber-400 font-semibold">Rolling Magma</span> + <span className="text-amber-400 font-semibold">Elemental Proliferation</span> + <span className="text-amber-400 font-semibold">Combustion</span>
                </p>
              </div>
              <div className="p-4 bg-red-900/20 rounded-lg border border-red-500/30">
                <h4 className="font-semibold text-red-400 mb-2">Single Target</h4>
                <p className="text-slate-300 text-sm">
                  <span className="text-amber-400 font-semibold">Fire Trap</span> + <span className="text-amber-400 font-semibold">Trap and Mine Damage</span> + <span className="text-amber-400 font-semibold">Swift Assembly</span>
                </p>
              </div>
            </div>
          </div>

          {/* Transition to Power Siphon */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-indigo-500/20">
            <h3 className="text-xl font-semibold text-indigo-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
              Transition to Power Siphon Mines (Level 31+)
            </h3>
            <p className="text-slate-300 leading-relaxed">
              At level 31, after completing the Siosa quest in the Library, you can acquire <span className="text-amber-400 font-semibold">Charged Mines</span>. This is when you transition to <span className="text-amber-400 font-semibold">Power Siphon</span> linked with <span className="text-amber-400 font-semibold">Locus Mines</span>.
            </p>
          </div>

          {/* Act 4 and Beyond */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-orange-500/20">
            <h3 className="text-xl font-semibold text-orange-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Act 4 and Beyond
            </h3>
            <p className="text-slate-300 leading-relaxed">
              Equip the <span className="text-amber-400 font-semibold">Grace</span> aura as soon as you get it in Act 4. You will feel too squishy without it. Regularly upgrade your gear, prioritizing high Evasion/Energy Shield bases, especially for your chest piece.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LevelingWalkthrough;