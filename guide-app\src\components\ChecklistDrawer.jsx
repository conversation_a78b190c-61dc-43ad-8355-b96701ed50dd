import React, { useState, useEffect } from 'react';

const initialTasks = [
  { id: 'lvl1-12-1', text: 'Get Rolling Magma + Ele Prolif', done: false },
  { id: 'lvl1-12-2', text: 'Get Flame Wall', done: false },
  { id: 'lvl1-12-3', text: 'Get Fire Trap', done: false },
  { id: 'lvl1-12-4', text: 'Look for 3-link items (BBB, GGB)', done: false },
  { id: 'lvl13-31-1', text: 'Complete Library quest', done: false },
  { id: 'lvl13-31-2', text: 'Link Fire Trap + Trap/Mine Dmg', done: false },
  { id: 'lvl13-31-3', text: 'Choose Bandit', done: false },
  { id: 'lvl13-31-4', text: 'Level Exsanguinate in off-hand', done: false },
  { id: 'lvl13-31-5', text: 'Transition to Power Siphon Mines', done: false },
  { id: 'lvl32-55-1', text: 'Get Grace aura', done: false },
  { id: 'lvl32-55-2', text: 'Complete first Labyrinth', done: false },
  { id: 'lvl32-55-3', text: 'Upgrade wand regularly', done: false },
  { id: 'lvl32-55-4', text: 'Look for 4-link items', done: false },
  { id: 'lvl56-70-1', text: 'Complete second Labyrinth', done: false },
  { id: 'lvl56-70-2', text: 'Complete third Labyrinth', done: false },
  { id: 'lvl56-70-3', text: 'Cap elemental resistances (75%)', done: false },
  { id: 'lvl56-70-4', text: 'Get a 5-link armor', done: false },
  { id: 'maps-1', text: 'Fix flasks, get ailment immunity', done: false },
  { id: 'maps-2', text: 'Upgrade gear to T1/T2 bases', done: false },
  { id: 'maps-3', text: 'Complete fourth Labyrinth', done: false },
  { id: 'maps-4', text: 'Start Atlas completion', done: false },
  { id: 'maps-5', text: 'Acquire transition items for Exsang', done: false },
];

const LOCAL_STORAGE_KEY = 'poe-guide-checklist';

function ChecklistDrawer() {
  const [isOpen, setIsOpen] = useState(false);
  const [tasks, setTasks] = useState(() => {
    const storedTasks = localStorage.getItem(LOCAL_STORAGE_KEY);
    return storedTasks ? JSON.parse(storedTasks) : initialTasks;
  });

  useEffect(() => {
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(tasks));
  }, [tasks]);

  const toggleTask = (id) => {
    setTasks(
      tasks.map((task) =>
        task.id === id ? { ...task, done: !task.done } : task
      )
    );
  };

  const completedTasks = tasks.filter(task => task.done).length;
  const totalTasks = tasks.length;
  const progressPercentage = Math.round((completedTasks / totalTasks) * 100);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="btn-secondary group relative overflow-hidden"
      >
        <span className="relative z-10 flex items-center space-x-2">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
          </svg>
          <span>Checklist</span>
          <span className="bg-slate-900/50 text-amber-400 text-xs px-2 py-1 rounded-full font-bold">
            {completedTasks}/{totalTasks}
          </span>
        </span>
      </button>

      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Drawer */}
      <div
        className={`fixed top-0 right-0 h-full w-96 bg-slate-800/95 backdrop-blur-md shadow-2xl transform transition-all duration-300 z-50 border-l border-white/10 ${
          isOpen ? 'translate-x-0' : 'translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-6 border-b border-white/10">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent">
                  Leveling Checklist
                </h3>
                <p className="text-sm text-slate-400 mt-1">
                  Track your progress through the guide
                </p>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="btn-ghost p-2 hover:bg-red-500/20 hover:text-red-400 transition-colors duration-200"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-400">Progress</span>
                <span className="text-amber-400 font-bold">{progressPercentage}%</span>
              </div>
              <div className="w-full bg-slate-900 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-amber-500 to-orange-500 h-3 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Task List */}
          <div className="flex-1 overflow-y-auto p-6 space-y-3">
            {tasks.map((task) => (
              <div
                key={task.id}
                className={`card p-4 transition-all duration-200 hover:scale-[1.02] ${
                  task.done ? 'bg-indigo-500/10 border-indigo-500/30' : ''
                }`}
              >
                <label className="flex items-start space-x-3 cursor-pointer group">
                  <div className="relative mt-1">
                    <input
                      type="checkbox"
                      checked={task.done}
                      onChange={() => toggleTask(task.id)}
                      className="sr-only"
                    />
                    <div className={`w-5 h-5 rounded border-2 transition-all duration-200 flex items-center justify-center ${
                      task.done
                        ? 'bg-indigo-500 border-indigo-500'
                        : 'border-slate-400 group-hover:border-indigo-500'
                    }`}>
                      {task.done && (
                        <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                        </svg>
                      )}
                    </div>
                  </div>
                  <span className={`text-sm leading-relaxed transition-all duration-200 ${
                    task.done
                      ? 'line-through text-slate-400'
                      : 'text-slate-200 group-hover:text-white'
                  }`}>
                    {task.text}
                  </span>
                </label>
              </div>
            ))}
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-white/10">
            <div className="text-center text-sm text-slate-400">
              {completedTasks === totalTasks ? (
                <span className="text-amber-400 font-semibold">🎉 All tasks completed!</span>
              ) : (
                <span>{totalTasks - completedTasks} tasks remaining</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default ChecklistDrawer;