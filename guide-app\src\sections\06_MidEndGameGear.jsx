import React from 'react';

const MidEndGameGear = () => {
  return (
    <section id="mid-end-game-gear" className="section">
      <div className="card mx-6 my-8">
        <div className="card-header">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-amber-400 to-orange-400 bg-clip-text text-transparent mb-4">
            Mid & End-Game Gearing
          </h2>
          <div className="w-20 h-1 bg-gradient-to-r from-indigo-500 to-blue-500 rounded-full"></div>
        </div>

        <div className="card-content space-y-8">
          {/* Unique Items */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-violet-500/20">
            <h3 className="text-xl font-semibold text-violet-400 mb-6 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
              </svg>
              Possible Unique Items
            </h3>
            <div className="grid gap-4">
              {[
                {
                  name: "Kintsugi",
                  description: "An amazing defensive chest piece that provides a significant layer of safety when you are hit.",
                  type: "Defensive",
                  color: "green"
                },
                {
                  name: "Restless Ward",
                  description: "A common and powerful option. It can be found with good corruptions for a reasonable price and helps with charge generation.",
                  type: "Utility",
                  color: "blue"
                },
                {
                  name: "Heatshiver",
                  description: "A massive damage multiplier, but can be expensive early in the league. Essential for pushing top-end damage.",
                  type: "Damage",
                  color: "red"
                },
                {
                  name: "Hrimsorrow",
                  description: "Excellent for easing the transition by providing 100% cold conversion on their own.",
                  type: "Conversion",
                  color: "cyan"
                }
              ].map((item, index) => (
                <div key={index} className={`p-5 bg-slate-800/50 rounded-lg border border-${item.color}-500/20 hover:border-${item.color}-500/40 transition-colors duration-200`}>
                  <div className="flex items-start justify-between mb-3">
                    <h4 className="text-amber-400 font-bold text-lg">{item.name}</h4>
                    <span className={`px-3 py-1 bg-${item.color}-500/20 text-${item.color}-400 text-xs font-medium rounded-full`}>
                      {item.type}
                    </span>
                  </div>
                  <p className="text-slate-300 text-sm leading-relaxed">{item.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Critical Strike Chance */}
          <div className="bg-slate-900/30 p-6 rounded-xl border border-red-500/20">
            <h3 className="text-xl font-semibold text-red-400 mb-4 flex items-center">
              <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Critical Strike Chance
            </h3>
            <p className="text-slate-300 leading-relaxed mb-6">
              Crit chance is crucial for both damage and freezing enemies. Your goal is to be crit-capped (100%) or as close as possible. Here are ways to get more:
            </p>
            <div className="grid gap-4">
              {[
                {
                  source: "Passive Tree",
                  method: "Nodes like High Voltage and medium cluster jewels are excellent sources.",
                  icon: "M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a1 1 0 01-1-1V9a1 1 0 011-1h1a2 2 0 100-4H4a1 1 0 01-1-1V4a1 1 0 011-1h3a1 1 0 001-1v-1a2 2 0 114 0z",
                  color: "purple"
                },
                {
                  source: "Gear",
                  method: "Look for \"Increased Critical Strike Chance for Spells\" on your shield and weapon.",
                  icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
                  color: "blue"
                },
                {
                  source: "Flasks",
                  method: "A Diamond Flask provides a huge conditional boost.",
                  icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
                  color: "green"
                },
                {
                  source: "Watcher's Eye",
                  method: "A Hatred Watcher's Eye can provide base critical strike chance.",
                  icon: "M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z",
                  color: "indigo"
                },
                {
                  source: "Curses",
                  method: "Assassin's Mark is a powerful option for single target.",
                  icon: "M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2h4a1 1 0 011 1v2a1 1 0 01-1 1h-1v10a2 2 0 01-2 2H6a2 2 0 01-2-2V8H3a1 1 0 01-1-1V5a1 1 0 011-1h4z",
                  color: "orange"
                },
                {
                  source: "Support Gems",
                  method: "If you are struggling, use the Increased Critical Strikes support gem.",
                  icon: "M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z",
                  color: "yellow"
                }
              ].map((item, index) => (
                <div key={index} className={`flex items-start space-x-4 p-4 bg-slate-800/50 rounded-lg border border-${item.color}-500/20`}>
                  <div className={`w-10 h-10 bg-${item.color}-500/20 text-${item.color}-400 rounded-lg flex items-center justify-center flex-shrink-0`}>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={item.icon} />
                    </svg>
                  </div>
                  <div>
                    <h4 className={`font-semibold text-${item.color}-400 mb-1`}>{item.source}</h4>
                    <p className="text-slate-300 text-sm leading-relaxed">
                      {item.method.split(/(\b(?:High Voltage|Diamond Flask|Hatred|Watcher's Eye|Assassin's Mark|Increased Critical Strikes)\b)/g).map((part, i) =>
                        ['High Voltage', 'Diamond Flask', 'Hatred', "Watcher's Eye", "Assassin's Mark", 'Increased Critical Strikes'].includes(part) ?
                        <span key={i} className="text-amber-400 font-semibold">{part}</span> : part
                      )}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MidEndGameGear;