@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --gaming-bg: #0a0e1a;
    --gaming-bg-secondary: #1e293b;
    --gaming-accent: #6366f1;
    --gaming-gold: #f59e0b;
    --gaming-text: #f8fafc;
    --gaming-text-muted: #94a3b8;
  }

  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-slate-900 text-slate-100 font-sans;
    background-image:
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    background-attachment: fixed;
    font-family: 'Inter', system-ui, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Enhanced <PERSON><PERSON> Styles */
  .btn-primary {
    @apply px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white font-medium rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-secondary {
    @apply px-6 py-3 bg-gradient-to-r from-amber-500 to-orange-500 text-slate-900 font-medium rounded-lg shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 active:scale-95;
  }

  .btn-ghost {
    @apply px-4 py-2 bg-slate-800/50 text-slate-200 border border-indigo-500/30 rounded-lg transition-all duration-300 hover:bg-indigo-500/20 hover:border-indigo-500/60 hover:shadow-lg;
  }

  /* Card Styles */
  .card {
    @apply bg-gradient-to-br from-slate-800/80 to-slate-900/90 backdrop-blur-sm border border-white/10 rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:border-white/20;
  }

  .card-header {
    @apply p-6 border-b border-white/10;
  }

  .card-content {
    @apply p-6;
  }

  /* Navigation Styles */
  .nav-item {
    @apply block rounded-lg px-4 py-3 text-sm font-medium transition-all duration-300 relative overflow-hidden;
  }

  .nav-item::before {
    @apply absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-blue-500/20 opacity-0 transition-opacity duration-300;
    content: '';
  }

  .nav-item:hover::before {
    @apply opacity-100;
  }

  .nav-item.active {
    @apply bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-lg;
  }

  .nav-item:not(.active) {
    @apply text-slate-400 hover:text-slate-200;
  }

  /* Section Styles */
  .section {
    @apply relative;
  }

  .section::before {
    @apply absolute top-0 left-0 w-1 h-full bg-gradient-to-b from-indigo-500 to-blue-500 opacity-60;
    content: '';
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-900;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-indigo-500/50 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-indigo-500/70;
  }
}